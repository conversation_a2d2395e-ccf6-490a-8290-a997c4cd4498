class TodoApp {
    constructor() {
        this.todos = this.migrateTodos(JSON.parse(localStorage.getItem('todos')) || []);
        this.currentFilter = 'all';
        this.currentCategory = 'all';
        this.editingId = null;
        this.viewingId = null;
        this.isLoading = false;
        // 移除搜索相关属性
        this.longPressTimer = null;
        this.longPressTarget = null;
        this.contextMenuVisible = false;

        this.initElements();
        this.bindEvents();
        this.render();
        this.showWelcomeMessage();

        // Android WebView 兼容性初始化
        this.initAndroidWebViewSupport();
    }

    // 迁移旧数据格式到新格式
    migrateTodos(todos) {
        return todos.map(todo => {
            // 确保每个todo都有必需的字段
            const migratedTodo = {
                id: todo.id || this.generateUniqueId(),
                title: todo.title || todo.text || '未命名任务',
                note: todo.note || '',
                completed: Boolean(todo.completed),
                priority: todo.priority || 'normal',
                category: todo.category || 'personal',
                dueDate: todo.dueDate || null,
                createdAt: todo.createdAt || new Date().toISOString(),
                updatedAt: todo.updatedAt || todo.createdAt || new Date().toISOString()
            };

            // 验证数据完整性
            if (!migratedTodo.title.trim()) {
                migratedTodo.title = '未命名任务';
            }

            return migratedTodo;
        });
    }

    // 生成唯一ID
    generateUniqueId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
    }

    initElements() {
        this.todoList = document.getElementById('todoList');
        this.filterBtns = document.querySelectorAll('.filter-btn');
        this.categoryFilter = document.getElementById('categoryFilter');
        this.totalCountNum = document.getElementById('totalCountNum');
        this.pendingCountNum = document.getElementById('pendingCountNum');
        this.completedCountNum = document.getElementById('completedCountNum');

        // 移除搜索相关元素初始化

        // 详情模态框
        this.detailModal = document.getElementById('detailModal');
        this.detailTitle = document.getElementById('detailTitle');
        this.detailTodoTitle = document.getElementById('detailTodoTitle');
        this.detailTodoNote = document.getElementById('detailTodoNote');
        this.detailPriority = document.getElementById('detailPriority');
        this.detailCategory = document.getElementById('detailCategory');
        this.detailDueDate = document.getElementById('detailDueDate');
        this.detailCreatedAt = document.getElementById('detailCreatedAt');
        this.closeDetailBtn = document.getElementById('closeDetailBtn');
        this.editDetailBtn = document.getElementById('editDetailBtn');
        this.deleteDetailBtn = document.getElementById('deleteDetailBtn');

        // 编辑模态框
        this.editModal = document.getElementById('editModal');
        this.editTitle = document.getElementById('editTitle');
        this.editNote = document.getElementById('editNote');
        this.editPriority = document.getElementById('editPriority');
        this.editCategory = document.getElementById('editCategory');
        this.editDueDate = document.getElementById('editDueDate');
        this.saveEditBtn = document.getElementById('saveEditBtn');
        this.cancelEditBtn = document.getElementById('cancelEditBtn');
        this.closeEditBtn = document.getElementById('closeEditBtn');
    }

    bindEvents() {
        // 过滤器
        this.filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });

        // 分类过滤
        this.categoryFilter.addEventListener('change', (e) => {
            this.setCategoryFilter(e.target.value);
        });

        // 移除搜索功能相关事件绑定

        // 详情模态框事件
        this.closeDetailBtn.addEventListener('click', () => this.closeDetailModal());
        this.editDetailBtn.addEventListener('click', () => this.editFromDetail());
        this.deleteDetailBtn.addEventListener('click', () => this.deleteFromDetail());
        this.detailModal.addEventListener('click', (e) => {
            if (e.target === this.detailModal) this.closeDetailModal();
        });

        // 编辑模态框事件
        this.saveEditBtn.addEventListener('click', () => this.saveEdit());
        this.cancelEditBtn.addEventListener('click', () => this.closeEditModal());
        this.closeEditBtn.addEventListener('click', () => this.closeEditModal());
        this.editModal.addEventListener('click', (e) => {
            if (e.target === this.editModal) this.closeEditModal();
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeDetailModal();
                this.closeEditModal();
                this.hideContextMenu();
            }
        });

        // 长按和上下文菜单事件
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.context-menu') && !e.target.closest('.todo-item')) {
                this.hideContextMenu();
            }
        });

        document.addEventListener('scroll', () => {
            this.hideContextMenu();
        });
    }

    goToAddPage() {
        window.location.href = 'add.html';
    }

    viewTodo(id) {
        const todo = this.todos.find(todo => todo.id === id);
        if (!todo) {
            this.showToast('待办事项不存在', 'error');
            return;
        }

        this.viewingId = id;
        this.detailTodoTitle.textContent = todo.title || '无标题';
        this.detailTodoNote.textContent = todo.note || '无备注';

        // 优先级显示
        this.detailPriority.textContent = this.getPriorityText(todo.priority || 'normal');
        this.detailPriority.className = `detail-value priority-badge ${todo.priority || 'normal'}`;

        // 分类显示
        this.detailCategory.textContent = this.getCategoryText(todo.category || 'personal');
        this.detailCategory.className = 'detail-value category-badge';

        // 截止日期
        if (todo.dueDate) {
            try {
                const dueDate = new Date(todo.dueDate);
                if (isNaN(dueDate.getTime())) {
                    throw new Error('Invalid date');
                }

                this.detailDueDate.textContent = dueDate.toLocaleDateString('zh-CN');

                const now = new Date();
                const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                const dueDateOnly = new Date(dueDate.getFullYear(), dueDate.getMonth(), dueDate.getDate());

                if (dueDateOnly < today && !todo.completed) {
                    this.detailDueDate.className = 'detail-value due-date overdue';
                } else if (dueDateOnly.getTime() === today.getTime()) {
                    this.detailDueDate.className = 'detail-value due-date today';
                } else {
                    this.detailDueDate.className = 'detail-value due-date';
                }
            } catch (error) {
                this.detailDueDate.textContent = '日期格式错误';
                this.detailDueDate.className = 'detail-value error';
            }
        } else {
            this.detailDueDate.textContent = '无截止日期';
            this.detailDueDate.className = 'detail-value';
        }

        // 创建时间
        try {
            const createdAt = new Date(todo.createdAt);
            if (isNaN(createdAt.getTime())) {
                throw new Error('Invalid date');
            }
            this.detailCreatedAt.textContent = createdAt.toLocaleString('zh-CN');
            this.detailCreatedAt.className = 'detail-value';
        } catch (error) {
            this.detailCreatedAt.textContent = '时间格式错误';
            this.detailCreatedAt.className = 'detail-value error';
        }

        this.detailModal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // 防止背景滚动
    }

    editFromDetail() {
        this.closeDetailModal();
        this.editTodo(this.viewingId);
    }

    deleteFromDetail() {
        if (confirm('确定要删除这个待办事项吗？')) {
            this.deleteTodo(this.viewingId);
            this.closeDetailModal();
        }
    }

    editTodo(id) {
        const todo = this.todos.find(todo => todo.id === id);
        if (!todo) {
            this.showToast('待办事项不存在', 'error');
            return;
        }

        this.editingId = id;
        this.editTitle.value = todo.title || '';
        this.editNote.value = todo.note || '';
        this.editPriority.value = todo.priority || 'normal';
        this.editCategory.value = todo.category || 'personal';
        this.editDueDate.value = todo.dueDate || '';

        this.editModal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // 防止背景滚动

        // 延迟聚焦以确保模态框完全显示
        setTimeout(() => {
            this.editTitle.focus();
            this.editTitle.select(); // 选中文本便于编辑
        }, 150);
    }

    saveEdit() {
        const title = this.editTitle.value.trim();
        if (!title) {
            this.showToast('请输入任务名称！');
            return;
        }

        const todo = this.todos.find(todo => todo.id === this.editingId);
        if (todo) {
            todo.title = title;
            todo.note = this.editNote.value.trim();
            todo.priority = this.editPriority.value;
            todo.category = this.editCategory.value;
            todo.dueDate = this.editDueDate.value || null;
            todo.updatedAt = new Date().toISOString();

            this.saveTodos();
            this.render();
            this.showToast('修改成功！');
        }

        this.closeEditModal();
    }

    deleteTodo(id) {
        this.todos = this.todos.filter(todo => todo.id !== id);
        this.saveTodos();
        this.render();
        this.showToast('删除成功！');
    }

    toggleTodo(id) {
        const todo = this.todos.find(todo => todo.id === id);
        if (todo) {
            const todoElement = document.querySelector(`[onclick*="${id}"]`);

            // 添加完成动画
            if (todoElement && !todo.completed) {
                todoElement.classList.add('completing');
                setTimeout(() => {
                    todoElement.classList.remove('completing');
                }, 500);
            }

            todo.completed = !todo.completed;
            todo.updatedAt = new Date().toISOString();
            this.saveTodos();

            // 延迟渲染以显示动画
            setTimeout(() => {
                this.render();

                // 显示反馈消息
                const message = todo.completed ? '任务已完成！' : '任务已恢复';
                this.showToast(message, todo.completed ? 'success' : 'info');
            }, todo.completed ? 300 : 0);

            // 触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(todo.completed ? [50, 30, 50] : 50);
            }
        }
    }

    closeDetailModal() {
        this.detailModal.style.display = 'none';
        this.viewingId = null;
        document.body.style.overflow = ''; // 恢复背景滚动
    }

    closeEditModal() {
        this.editModal.style.display = 'none';
        this.editingId = null;
        document.body.style.overflow = ''; // 恢复背景滚动
    }

    setFilter(filter) {
        this.currentFilter = filter;
        this.filterBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.filter === filter);
        });
        this.render();
    }

    setCategoryFilter(category) {
        this.currentCategory = category;
        this.render();
    }

    getFilteredTodos() {
        let filtered = this.todos;

        // 按状态过滤
        if (this.currentFilter === 'pending') {
            filtered = filtered.filter(todo => !todo.completed);
        } else if (this.currentFilter === 'completed') {
            filtered = filtered.filter(todo => todo.completed);
        }

        // 按分类过滤
        if (this.currentCategory !== 'all') {
            filtered = filtered.filter(todo => todo.category === this.currentCategory);
        }

        // 移除搜索过滤逻辑

        return filtered;
    }

    // 移除所有搜索相关方法
    // searchTodos, handleSearch, clearSearch 等方法都可以删除

    getPriorityText(priority) {
        const priorityMap = {
            low: '低',
            normal: '普通',
            high: '高',
            urgent: '紧急'
        };
        return priorityMap[priority] || '普通';
    }

    getCategoryText(category) {
        const categoryMap = {
            personal: '个人',
            work: '工作',
            study: '学习',
            life: '生活',
            other: '其他'
        };
        return categoryMap[category] || '个人';
    }

    formatDueDate(dueDate) {
        if (!dueDate) return '';

        const due = new Date(dueDate);
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const dueDay = new Date(due.getFullYear(), due.getMonth(), due.getDate());

        const diffTime = dueDay - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 0) {
            return `逾期 ${Math.abs(diffDays)} 天`;
        } else if (diffDays === 0) {
            return '今天到期';
        } else if (diffDays === 1) {
            return '明天到期';
        } else if (diffDays <= 7) {
            return `${diffDays} 天后到期`;
        } else {
            return due.toLocaleDateString('zh-CN');
        }
    }

    // 在 render() 方法中更新待办事项的HTML结构
    render() {
        const filteredTodos = this.getFilteredTodos();

        if (filteredTodos.length === 0) {
            this.todoList.innerHTML = `
                <div class="empty-state">
                    <h3>📝</h3>
                    <p>${this.getEmptyMessage()}</p>
                </div>
            `;
        } else {
            this.todoList.innerHTML = filteredTodos.map(todo => {
                const dueDate = this.formatDueDate(todo.dueDate);
                const isOverdue = todo.dueDate && new Date(todo.dueDate) < new Date() && !todo.completed;
                const isToday = todo.dueDate && new Date(todo.dueDate).toDateString() === new Date().toDateString();

                return `
                    <div class="todo-item ${todo.completed ? 'completed' : ''}"
                         data-todo-id="${todo.id}"
                         onclick="app.viewTodo(${todo.id})"
                         oncontextmenu="event.preventDefault(); app.showContextMenu(event, ${todo.id})"
                         ontouchstart="app.handleTouchStart(event, ${todo.id})"
                         ontouchend="app.handleTouchEnd(event)"
                         ontouchmove="app.handleTouchMove(event)">

                        <input type="checkbox" class="todo-checkbox" ${todo.completed ? 'checked' : ''}
                               onclick="event.stopPropagation(); app.toggleTodo(${todo.id})"
                               ontouchstart="event.stopPropagation()"
                               ontouchend="event.stopPropagation()">

                        <div class="todo-content">
                            <div class="todo-text">${this.escapeHtml(todo.title)}</div>

                            <div class="todo-meta">
                                <span class="todo-priority ${todo.priority}">${this.getPriorityText(todo.priority)}</span>
                                <span class="todo-category">${this.getCategoryText(todo.category)}</span>
                                ${todo.dueDate ? `<span class="todo-due-date ${isOverdue ? 'overdue' : isToday ? 'today' : ''}">${dueDate}</span>` : ''}
                            </div>
                        </div>

                        <div class="long-press-hint">
                            <span class="hint-text">长按显示更多选项</span>
                        </div>
                    </div>
                `;
            }).join('');
        }

        this.updateStats();
    }

    getEmptyMessage() {
        if (this.searchQuery) {
            return `没有找到包含"${this.searchQuery}"的任务\n试试其他关键词吧！`;
        }

        if (this.currentCategory !== 'all') {
            return `${this.getCategoryText(this.currentCategory)}分类下没有${this.currentFilter === 'pending' ? '待完成的' : this.currentFilter === 'completed' ? '已完成的' : ''}事项`;
        }

        switch (this.currentFilter) {
            case 'pending':
                return '没有待完成的事项\n休息一下吧！';
            case 'completed':
                return '还没有完成任何事项\n加油完成第一个吧！';
            default:
                return '还没有任何待办事项\n点击右下角按钮添加一个吧！';
        }
    }

    updateStats() {
        const total = this.todos.length;
        const completed = this.todos.filter(todo => todo.completed).length;
        const pending = total - completed;

        this.totalCountNum.textContent = total;
        this.pendingCountNum.textContent = pending;
        this.completedCountNum.textContent = completed;
    }

    showToast(message, type = 'info') {
        // 移除现有的toast
        const existingToast = document.querySelector('.app-toast');
        if (existingToast) {
            existingToast.remove();
        }

        const toast = document.createElement('div');
        toast.className = 'app-toast';

        const colors = {
            success: { bg: '#28a745', icon: '✅' },
            error: { bg: '#dc3545', icon: '❌' },
            warning: { bg: '#ffc107', icon: '⚠️' },
            info: { bg: '#17a2b8', icon: 'ℹ️' }
        };

        const color = colors[type] || colors.info;

        toast.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: ${color.bg};
            color: white;
            padding: 16px 24px;
            border-radius: 30px;
            font-size: 14px;
            font-weight: 600;
            z-index: 9999;
            pointer-events: none;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 300px;
            text-align: center;
        `;

        toast.innerHTML = `<span>${color.icon}</span><span>${message}</span>`;
        document.body.appendChild(toast);

        // 显示动画
        requestAnimationFrame(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translate(-50%, -50%) scale(1)';
        });

        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translate(-50%, -50%) scale(0.9)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, type === 'error' ? 3000 : 2000);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    saveTodos() {
        try {
            localStorage.setItem('todos', JSON.stringify(this.todos));
            return true;
        } catch (error) {
            console.error('保存数据失败:', error);
            this.showToast('保存失败，请检查存储空间', 'error');
            return false;
        }
    }

    // 显示欢迎消息
    showWelcomeMessage() {
        if (this.todos.length === 0) {
            setTimeout(() => {
                this.showToast('欢迎使用待办事项管理！点击右下角按钮添加第一个任务', 'info');
            }, 1000);
        }
    }

    // 添加搜索功能
    searchTodos(query) {
        if (!query.trim()) {
            return this.todos;
        }

        const searchTerm = query.toLowerCase();
        return this.todos.filter(todo =>
            todo.title.toLowerCase().includes(searchTerm) ||
            (todo.note && todo.note.toLowerCase().includes(searchTerm))
        );
    }

    // 添加批量操作功能
    markAllCompleted() {
        const pendingTodos = this.todos.filter(todo => !todo.completed);
        if (pendingTodos.length === 0) {
            this.showToast('没有待完成的任务', 'info');
            return;
        }

        pendingTodos.forEach(todo => {
            todo.completed = true;
            todo.updatedAt = new Date().toISOString();
        });

        this.saveTodos();
        this.render();
        this.showToast(`已完成 ${pendingTodos.length} 个任务`, 'success');
    }

    // 清除已完成的任务
    clearCompleted() {
        const completedCount = this.todos.filter(todo => todo.completed).length;
        if (completedCount === 0) {
            this.showToast('没有已完成的任务', 'info');
            return;
        }

        if (confirm(`确定要删除 ${completedCount} 个已完成的任务吗？`)) {
            this.todos = this.todos.filter(todo => !todo.completed);
            this.saveTodos();
            this.render();
            this.showToast(`已删除 ${completedCount} 个已完成任务`, 'success');
        }
    }

    // 搜索处理
    handleSearch(query) {
        this.searchQuery = query.trim();

        if (this.searchQuery) {
            this.searchClear.style.display = 'block';
        } else {
            this.searchClear.style.display = 'none';
        }

        this.render();
    }

    // 清除搜索
    clearSearch() {
        this.searchInput.value = '';
        this.searchQuery = '';
        this.searchClear.style.display = 'none';
        this.render();
        this.searchInput.focus();
    }

    // 导出数据
    exportData() {
        try {
            const data = {
                todos: this.todos,
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `todos-backup-${new Date().toISOString().split('T')[0]}.json`;

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.showToast('数据导出成功！', 'success');
        } catch (error) {
            console.error('导出失败:', error);
            this.showToast('导出失败，请重试', 'error');
        }
    }

    // 处理触摸开始
    handleTouchStart(event, todoId) {
        if (event.target.closest('.todo-checkbox')) {
            return; // 如果点击的是复选框，不处理长按
        }

        this.longPressTarget = todoId;
        const todoElement = event.currentTarget;

        this.longPressTimer = setTimeout(() => {
            // 添加长按视觉反馈
            todoElement.classList.add('long-pressing');

            this.showContextMenu(event, todoId);

            // 触觉反馈
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            // 移除视觉反馈
            setTimeout(() => {
                todoElement.classList.remove('long-pressing');
            }, 200);
        }, 500); // 500ms长按
    }

    // 处理触摸结束
    handleTouchEnd(event) {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    }

    // 处理触摸移动
    handleTouchMove(event) {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    }

    // 显示上下文菜单
    showContextMenu(event, todoId) {
        event.preventDefault();
        event.stopPropagation();

        this.hideContextMenu(); // 先隐藏现有菜单

        const todo = this.todos.find(t => t.id === todoId);
        if (!todo) return;

        const contextMenu = document.createElement('div');
        contextMenu.className = 'context-menu';
        contextMenu.innerHTML = `
            <div class="context-menu-item" onclick="app.viewTodo(${todoId}); app.hideContextMenu();">
                <span class="menu-icon">👁️</span>
                <span class="menu-text">查看详情</span>
            </div>
            <div class="context-menu-item" onclick="app.editTodo(${todoId}); app.hideContextMenu();">
                <span class="menu-icon">✏️</span>
                <span class="menu-text">编辑</span>
            </div>
            <div class="context-menu-item" onclick="app.toggleTodo(${todoId}); app.hideContextMenu();">
                <span class="menu-icon">${todo.completed ? '↩️' : '✅'}</span>
                <span class="menu-text">${todo.completed ? '标记未完成' : '标记完成'}</span>
            </div>
            <div class="context-menu-divider"></div>
            <div class="context-menu-item danger" onclick="app.confirmDelete(${todoId}); app.hideContextMenu();">
                <span class="menu-icon">🗑️</span>
                <span class="menu-text">删除</span>
            </div>
        `;

        // 计算菜单位置
        let x, y;
        if (event.touches && event.touches[0]) {
            x = event.touches[0].clientX;
            y = event.touches[0].clientY;
        } else {
            x = event.clientX;
            y = event.clientY;
        }

        document.body.appendChild(contextMenu);

        // 调整位置避免超出屏幕
        const rect = contextMenu.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        if (x + rect.width > viewportWidth) {
            x = viewportWidth - rect.width - 10;
        }
        if (y + rect.height > viewportHeight) {
            y = viewportHeight - rect.height - 10;
        }

        contextMenu.style.left = `${Math.max(10, x)}px`;
        contextMenu.style.top = `${Math.max(10, y)}px`;

        this.contextMenuVisible = true;

        // 添加显示动画
        setTimeout(() => {
            contextMenu.classList.add('show');
        }, 10);
    }

    // 隐藏上下文菜单
    hideContextMenu() {
        const contextMenu = document.querySelector('.context-menu');
        if (contextMenu) {
            contextMenu.classList.remove('show');
            setTimeout(() => {
                if (contextMenu.parentNode) {
                    contextMenu.parentNode.removeChild(contextMenu);
                }
            }, 200);
        }
        this.contextMenuVisible = false;
    }

    // 确认删除
    confirmDelete(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (!todo) return;

        if (confirm(`确定要删除"${todo.title}"吗？`)) {
            this.deleteTodo(todoId);
        }
    }
}

// 全局函数
function goToAddPage() {
    window.location.href = 'add.html';
}

// 初始化应用
const app = new TodoApp();

// PWA支持
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}